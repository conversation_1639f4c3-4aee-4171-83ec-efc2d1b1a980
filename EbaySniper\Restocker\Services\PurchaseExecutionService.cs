﻿using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using uBuyFirst.Data;
using uBuyFirst.Purchasing;

namespace uBuyFirst.Restocker.Services
{
    /// <summary>
    /// Service responsible for executing purchase attempts through the Restocker system.
    ///
    /// CRITICAL BUSINESS RULE: This service enforces over-purchase prevention by ensuring that
    /// PurchasedQuantity never exceeds RequiredQuantity for any JobId. This prevents budget
    /// overruns and ensures precise quantity control in the Restocker module.
    ///
    /// The service automatically stops purchasing when the required quantity is reached and
    /// calculates exact quantities needed to prevent over-purchasing.
    ///
    /// CONCURRENCY CONTROL: Uses SemaphoreSlim per JobId to prevent race conditions where
    /// multiple concurrent purchase attempts could bypass quantity limits before PurchasedQuantity
    /// is updated. This ensures thread-safe quantity checking and purchasing.
    /// </summary>
    public class PurchaseExecutionService : IDisposable
    {
        private bool _disposed = false;

        /// <summary>
        /// Semaphores for controlling concurrent access per JobId to prevent over-purchasing
        /// </summary>
        private static readonly ConcurrentDictionary<string, SemaphoreSlim> _jobSemaphores = new();

        /// <summary>
        /// Cleanup timer to remove unused semaphores and prevent memory leaks
        /// </summary>
        private static readonly Timer _cleanupTimer = new(CleanupUnusedSemaphores, null, TimeSpan.FromMinutes(10), TimeSpan.FromMinutes(10));

        public PurchaseExecutionService()
        {
        }

        /// <summary>
        /// Gets or creates a semaphore for the specified JobId to control concurrent purchases
        /// </summary>
        /// <param name="jobId">The JobId to get a semaphore for</param>
        /// <returns>SemaphoreSlim instance for the JobId</returns>
        private static SemaphoreSlim GetJobSemaphore(string jobId)
        {
            return _jobSemaphores.GetOrAdd(jobId, _ => new SemaphoreSlim(1, 1));
        }

        /// <summary>
        /// Cleanup method to remove unused semaphores and prevent memory leaks
        /// </summary>
        /// <param name="state">Timer state (unused)</param>
        private static void CleanupUnusedSemaphores(object state)
        {
            try
            {
                var keysToRemove = new List<string>();

                foreach (var kvp in _jobSemaphores)
                {
                    var semaphore = kvp.Value;
                    // If semaphore is available (not being used) and has been idle, consider removing it
                    // This is a simple heuristic - in production you might want more sophisticated cleanup
                    if (semaphore.CurrentCount == 1)
                    {
                        // Try to remove unused semaphores (this is a best-effort cleanup)
                        if (_jobSemaphores.TryRemove(kvp.Key, out var removedSemaphore))
                        {
                            removedSemaphore?.Dispose();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log cleanup errors but don't let them crash the application
                System.Diagnostics.Debug.WriteLine($"Error during semaphore cleanup: {ex.Message}");
            }
        }

        /// <summary>
        /// Attempts to purchase an item for a specific keyword's restock requirements.
        /// Uses semaphore-based concurrency control to prevent race conditions where multiple
        /// concurrent purchase attempts could bypass quantity limits.
        /// </summary>
        /// <param name="dataList">The eBay item to potentially purchase</param>
        /// <param name="keyword">The keyword with restock requirements</param>
        /// <param name="filterAlias">The filter alias that triggered this purchase attempt</param>
        /// <returns>Result of the purchase attempt</returns>
        public async Task<PurchaseExecutionResult> TryPurchaseItemAsync(DataList dataList, Keyword2Find keyword, string filterAlias)
        {
            if (dataList == null)
                throw new ArgumentNullException(nameof(dataList));
            if (keyword == null)
                throw new ArgumentNullException(nameof(keyword));

            // Check if keyword has restock configuration before acquiring semaphore
            if (string.IsNullOrEmpty(keyword.JobId) || keyword.RequiredQuantity <= 0)
            {
                return PurchaseExecutionResult.CreateSkipped("Keyword has no restock configuration");
            }

            // Get semaphore for this JobId to prevent concurrent purchases
            var semaphore = GetJobSemaphore(keyword.JobId);

            try
            {
                // Acquire semaphore with timeout to prevent indefinite blocking
                var acquired = await semaphore.WaitAsync(TimeSpan.FromSeconds(30)).ConfigureAwait(false);
                if (!acquired)
                {
                    return PurchaseExecutionResult.CreateFailure("Timeout waiting for purchase lock - another purchase may be in progress", null);
                }

                try
                {
                    // OVER-PURCHASE PREVENTION: Re-check quantity after acquiring semaphore
                    // This prevents race conditions where multiple threads pass the initial check
                    if (keyword.PurchasedQuantity >= keyword.RequiredQuantity)
                    {
                        return PurchaseExecutionResult.CreateSkipped("Required quantity already reached");
                    }

                    // Process the purchase for this keyword
                    var result = await ProcessKeywordPurchaseAsync(dataList, keyword, filterAlias);
                    return result;
                }
                finally
                {
                    // Always release the semaphore
                    semaphore.Release();
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error during purchase execution for item {dataList.ItemID}: {ex.Message}";
                return PurchaseExecutionResult.CreateFailure(errorMessage, ex);
            }
        }

        /// <summary>
        /// Processes a purchase attempt for a specific keyword
        /// </summary>
        private async Task<PurchaseExecutionResult> ProcessKeywordPurchaseAsync(
            DataList dataList, Keyword2Find keyword, string filterAlias)
        {
            try
            {
                // Check if credit card checkout is available
                if (!CreditCardService.CreditCardPaymentEnabled)
                {
                    var message = "Credit card payment is not enabled";
                    return PurchaseExecutionResult.CreateSkipped(message);
                }

                // Validate item availability
                if (dataList.QuantityAvailable <= 0)
                {
                    var message = "Item is not available for purchase";
                    return PurchaseExecutionResult.CreateSkipped(message);
                }

                // OVER-PURCHASE PREVENTION: Calculate exact quantity to purchase
                // This ensures we never purchase more than required (PurchasedQuantity <= RequiredQuantity)
                var remainingQuantity = keyword.RequiredQuantity - keyword.PurchasedQuantity;
                var quantityToPurchase = Math.Min(remainingQuantity, dataList.QuantityAvailable);

                // Double-check: If no quantity needed, skip the purchase
                if (quantityToPurchase <= 0)
                {
                    var message = "Purchase requirement already fulfilled";
                    return PurchaseExecutionResult.CreateSkipped(message);
                }

                // Execute the purchase
                var purchaseResult = await ExecutePurchaseAsync(dataList, quantityToPurchase, keyword, filterAlias);

                return purchaseResult;
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error processing purchase for keyword {keyword.Id}: {ex.Message}";
                return PurchaseExecutionResult.CreateFailure(errorMessage, ex);
            }
        }

        /// <summary>
        /// Executes the actual purchase using the existing CreditCardCheckout system
        /// </summary>
        private async Task<PurchaseExecutionResult> ExecutePurchaseAsync(DataList dataList, int quantity, Keyword2Find keyword, string filterAlias)
        {
            try
            {
                // Execute the credit card checkout with the calculated quantity
                await CreditCardCheckout.ExecuteCreditCardCheckout(dataList, quantity).ConfigureAwait(false);

                // Monitor the purchase status

                if (dataList.Order?.CheckoutStatus == BuyingService.Order.CheckoutState.PaymentSuccess
                    || dataList.Order?.CheckoutStatus == BuyingService.Order.CheckoutState.TestPurchase)
                {
                    // Update keyword's purchased quantity
                    keyword.PurchasedQuantity += quantity;

                    // Save settings to persist the updated purchase quantity
                    // Use thread-safe invocation to avoid cross-thread operation errors
                    await SaveSettingsThreadSafeAsync();

                    return PurchaseExecutionResult.CreateSuccess(
                        $"Successfully purchased {quantity} of item {dataList.ItemID} for job {keyword.JobId}",
                        quantity);
                }
                else
                {
                    var errorMessage = dataList.Order?.FailureReasonMessage ?? "Purchase failed for unknown reason";
                    return PurchaseExecutionResult.CreateFailure(errorMessage);
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error executing purchase: {ex.Message}";
                return PurchaseExecutionResult.CreateFailure(errorMessage, ex);
            }
        }




        /// <summary>
        /// Saves settings in a thread-safe manner to avoid cross-thread operation errors
        /// </summary>
        private async Task SaveSettingsThreadSafeAsync()
        {
            try
            {
                var form1 = Form1.Instance;
                if (form1 == null) return;

                // Check if we need to invoke on the UI thread
                if (form1.InvokeRequired)
                {
                    // Use TaskCompletionSource to make the synchronous Invoke call awaitable
                    var tcs = new TaskCompletionSource<bool>();

                    form1.BeginInvoke(new Action(() =>
                    {
                        try
                        {
                            form1.SaveSettings();
                            tcs.SetResult(true);
                        }
                        catch (Exception ex)
                        {
                            tcs.SetException(ex);
                        }
                    }));

                    // Wait for the UI thread operation to complete
                    await tcs.Task.ConfigureAwait(false);
                }
                else
                {
                    // Already on UI thread, call directly
                    form1.SaveSettings();
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't let it crash the purchase process
                System.Diagnostics.Debug.WriteLine($"Error saving settings after purchase: {ex.Message}");
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;
            }
        }

        /// <summary>
        /// Static cleanup method to dispose of all semaphores and cleanup timer
        /// Call this when shutting down the application
        /// </summary>
        public static void DisposeStaticResources()
        {
            try
            {
                _cleanupTimer?.Dispose();

                // Dispose all semaphores
                foreach (var semaphore in _jobSemaphores.Values)
                {
                    semaphore?.Dispose();
                }

                _jobSemaphores.Clear();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error disposing static resources: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Result of a purchase execution attempt
    /// </summary>
    public class PurchaseExecutionResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public Exception Exception { get; set; }
        public int QuantityPurchased { get; set; }

        public static PurchaseExecutionResult CreateSuccess(string message, int quantityPurchased = 0)
        {
            return new PurchaseExecutionResult
            {
                Success = true,
                Message = message,
                QuantityPurchased = quantityPurchased
            };
        }

        public static PurchaseExecutionResult CreateFailure(string message, Exception exception = null)
        {
            return new PurchaseExecutionResult
            {
                Success = false,
                Message = message,
                Exception = exception
            };
        }

        public static PurchaseExecutionResult CreateSkipped(string message)
        {
            return new PurchaseExecutionResult
            {
                Success = false,
                Message = message
            };
        }
    }
}
