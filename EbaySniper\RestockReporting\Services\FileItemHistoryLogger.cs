using System;
using System.IO;
using System.Threading.Tasks;
using Newtonsoft.Json;
using uBuyFirst.RestockReporting.Models;

namespace uBuyFirst.RestockReporting.Services
{
    /// <summary>
    /// File-based implementation of item history logging
    /// Creates individual JSON files for each item processing operation
    /// </summary>
    public class FileItemHistoryLogger : IItemHistoryLogger
    {
        private readonly ItemHistoryOptions _options;
        private bool _disposed = false;

        public FileItemHistoryLogger(ItemHistoryOptions options)
        {
            _options = options ?? throw new ArgumentNullException(nameof(options));
        }

        /// <summary>
        /// Logs item processing context to a JSON file
        /// </summary>
        public async Task LogItemProcessingAsync(ItemProcessingContext context)
        {
            if (_disposed)
                return;

            if (!_options.EnableLogging)
                return;

            if (context == null)
                return;

            try
            {
                var filePath = GenerateFilePath(context);

                // If purchase was successful and file already exists, ignore (don't overwrite)
                if (IsSuccessfulPurchase(context) && File.Exists(filePath))
                {
                    return; // Ignore - don't overwrite existing successful purchase file
                }

                var json = JsonConvert.SerializeObject(context, Formatting.Indented);

                // Ensure directory exists
                var directory = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Write asynchronously
                await Task.Run(() => File.WriteAllText(filePath, json));
            }
            catch (Exception ex)
            {
                // Log error but don't throw - continue processing other items
                await LogErrorAsync($"Failed to write item history for {context.ItemData?.ItemId}: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Determines if the context represents a successful purchase
        /// </summary>
        private bool IsSuccessfulPurchase(ItemProcessingContext context)
        {
            // A successful purchase is indicated by outcome being "purchased"
            // This aligns with DataListMapper.DetermineOutcome() logic
            return context?.Outcome == "purchased";
        }

        /// <summary>
        /// Generates the file path for an item processing context
        /// </summary>
        public string GenerateFilePath(ItemProcessingContext context)
        {
            var fileName = GenerateFileName(context);

            if (_options.CreateDailyFolders)
            {
                var dateFolder = context.Timestamp.ToString("yyyy-MM-dd");
                return Path.Combine(_options.BasePath, dateFolder, fileName);
            }
            else
            {
                return Path.Combine(_options.BasePath, fileName);
            }
        }

        /// <summary>
        /// Generates the file name for an item processing context
        /// Format: {ItemId}_{JobId}_{KeywordId}.json
        /// </summary>
        public string GenerateFileName(ItemProcessingContext context)
        {
            var itemId = context.ItemData?.ItemId ?? "unknown";
            var jobId = context.KeywordState?.JobId ?? "unknown";
            var keywordId = context.KeywordState?.KeywordId ?? "unknown";

            return $"{itemId}_{jobId}_{keywordId}.json";
        }



        /// <summary>
        /// Logs errors to the error log file
        /// </summary>
        private async Task LogErrorAsync(string message, Exception ex)
        {
            try
            {
                var errorLogFile = Path.Combine(_options.ErrorLogPath, $"{DateTime.Now:yyyy-MM-dd}_errors.log");
                var errorEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}\n{ex}\n\n";

                // Ensure error directory exists
                if (!Directory.Exists(_options.ErrorLogPath))
                {
                    Directory.CreateDirectory(_options.ErrorLogPath);
                }

                await Task.Run(() => File.AppendAllText(errorLogFile, errorEntry));
            }
            catch
            {
                // If we can't even log the error, just ignore it
                // Don't let error logging break the main processing
            }
        }

        /// <summary>
        /// Disposes of resources
        /// </summary>
        public void Dispose()
        {
            _disposed = true;
        }
    }
}
