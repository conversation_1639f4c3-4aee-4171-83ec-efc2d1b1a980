﻿using System;
using uBuyFirst.Data;
using uBuyFirst.RestockReporting.Models;
using uBuyFirst.Restocker.Services;

namespace uBuyFirst.RestockReporting.Services
{
    /// <summary>
    /// Maps DataList objects to ItemHistoryData for historical logging
    /// </summary>
    public static class DataListMapper
    {
        /// <summary>
        /// Extracts item data from a DataList object
        /// </summary>
        /// <param name="dataList">The DataList containing eBay item information</param>
        /// <returns>ItemHistoryData with all available properties populated</returns>
        public static ItemHistoryData ExtractItemData(DataList dataList)
        {
            if (dataList == null)
                return null;

            return new ItemHistoryData
            {
                ItemId = dataList.ItemID,
                Title = dataList.Title,
                CurrentPrice = (decimal)dataList.ItemPrice,
                Condition = dataList.Condition,
                Seller = dataList.SellerName,
                ShippingCost = (decimal)dataList.Shipping,
                Location = dataList.Location,
                EndTime = dataList.EndTime?.DateTime,
                ItemPrice = (decimal)dataList.ItemPrice,
                AuctionPrice = dataList.AuctionPrice > 0 ? (decimal)dataList.AuctionPrice : null,
                QuantityAvailable = dataList.QuantityAvailable,
                ListingType = dataList.ListingType?.ToString(),
                ItemUrl = dataList.GetAffiliateLink(),
                ImageUrl = dataList.GalleryUrl,
                CategoryId = dataList.CategoryID,
                CategoryName = dataList.CategoryName,
                ConditionDescription = dataList.ConditionDescription,
                Description = dataList.Description,
                FeedbackScore = dataList.FeedbackScore,
                FeedbackRating = dataList.FeedbackRating,
                BestOffer = dataList.BestOffer,
                Site = dataList.Site.ToString(),

                // New fields
                StartTime = dataList.StartTimeLocal?.DateTime,
                FoundTimeSeconds = (int)(dataList.FoundTime.TimeDiff ?? 0),
                FromCountry = dataList.FromCountry,
                SellerCountry = dataList.ToCountry, // Note: ToCountry might be seller's country
                ShippingDays = dataList.ShippingDays,
                DispatchDays = dataList.DispatchDays,
                ShippingDelivery = dataList.ShippingDelivery,
                ShippingType = dataList.ShippingType,
                TotalPrice = (decimal)dataList.TotalPrice,
                Brand = dataList.Brand,
                UPC = dataList.UPC,
                Model = dataList.Model,
                MPN = dataList.MPN,
                SellerIsBusiness = dataList.SellerIsBusiness
            };
        }

        /// <summary>
        /// Creates a FilterRuleContext from filter information
        /// </summary>
        /// <param name="filterAlias">The alias of the filter</param>
        /// <param name="expression">The filter expression</param>
        /// <param name="matched">Whether the filter matched</param>
        /// <param name="evaluationResult">The result of filter evaluation</param>
        /// <returns>FilterRuleContext with the provided information</returns>
        public static FilterRuleContext CreateFilterRuleContext(string filterAlias, string expression, bool matched, string evaluationResult)
        {
            return new FilterRuleContext
            {
                FilterAlias = filterAlias,
                Expression = expression,
                Matched = matched,
                EvaluationResult = evaluationResult,
                EvaluatedAt = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Creates a TransactionResult from a PurchaseExecutionResult and DataList
        /// </summary>
        /// <param name="purchaseResult">The result of the purchase attempt</param>
        /// <param name="dataList">The DataList containing transaction information</param>
        /// <returns>TransactionResult with the purchase information</returns>
        public static TransactionResult CreateTransactionResult(PurchaseExecutionResult purchaseResult, DataList dataList = null)
        {
            if (purchaseResult == null)
            {
                return new TransactionResult
                {
                    Attempted = false,
                    Success = false
                };
            }

            return new TransactionResult
            {
                Attempted = true,
                Success = purchaseResult.Success,
                ErrorMessage = purchaseResult.Success ? null : purchaseResult.Message,
                PurchasePrice = purchaseResult.Success ? (decimal?)dataList?.ItemPrice : null,
                Quantity = purchaseResult.QuantityPurchased > 0 ? purchaseResult.QuantityPurchased : null,
                CompletedAt = purchaseResult.Success ? DateTime.UtcNow : null
            };
        }

        /// <summary>
        /// Determines the outcome string based on filter match and purchase result
        /// </summary>
        /// <param name="filterMatched">Whether the filter matched</param>
        /// <param name="purchaseResult">The result of the purchase attempt (null if no attempt)</param>
        /// <returns>Outcome string for logging</returns>
        public static string DetermineOutcome(bool filterMatched, PurchaseExecutionResult purchaseResult)
        {
            if (!filterMatched)
                return "filtered_out";

            if (purchaseResult == null)
                return "no_action";

            if (purchaseResult.Success)
                return "purchased";

            if (purchaseResult.Message?.IndexOf("skipped", StringComparison.OrdinalIgnoreCase) >= 0)
                return "skipped";

            return "purchase_failed";
        }

        /// <summary>
        /// Creates a reason string based on filter match and purchase result
        /// </summary>
        /// <param name="filterMatched">Whether the filter matched</param>
        /// <param name="purchaseResult">The result of the purchase attempt (null if no attempt)</param>
        /// <returns>Reason string for logging</returns>
        public static string CreateReason(bool filterMatched, PurchaseExecutionResult purchaseResult)
        {
            if (!filterMatched)
                return "Item did not match restock filter criteria";

            if (purchaseResult == null)
                return "No purchase attempt was made";

            if (purchaseResult.Success)
                return $"Successfully purchased {purchaseResult.QuantityPurchased} items";

            return $"Purchase attempt failed: {purchaseResult.Message}";
        }
    }
}
